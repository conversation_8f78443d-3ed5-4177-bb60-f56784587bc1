﻿using System;
using System.IO;
using System.Xml.Linq;

// 测试原来的方法（使用 ToString()）
var xmlDoc1 = new XDocument(
    new XDeclaration("1.0", "utf-8", "yes"),
    new XElement("benchmark",
        new XElement("drive", "C:\\"),
        new XElement("description", "Test")
    )
);

Console.WriteLine("使用 ToString() 方法:");
Console.WriteLine(xmlDoc1.ToString());
Console.WriteLine();

// 测试修复后的方法（使用 Save()）
var xmlDoc2 = new XDocument(
    new XDeclaration("1.0", "utf-8", "yes"),
    new XElement("benchmark",
        new XElement("drive", "C:\\"),
        new XElement("description", "Test")
    )
);

Console.WriteLine("使用 Save() 方法:");
using (var writer = new StringWriter())
{
    xmlDoc2.Save(writer, SaveOptions.None);
    Console.WriteLine(writer.ToString());
}

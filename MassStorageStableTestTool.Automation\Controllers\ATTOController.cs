using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Common;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Automation.GUI;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Xml.Linq;
using System.Configuration;

namespace MassStorageStableTestTool.Automation.Controllers;

/// <summary>
/// ATTO Disk Benchmark 控制器
/// 支持CLI模式的磁盘性能测试
/// </summary>
public class ATTOController : BaseTestToolController
{
    private readonly ILogger<ATTOController> _logger;
    private readonly AutomationHelper _automationHelper;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">工具配置</param>
    /// <param name="automationHelper">自动化助手</param>
    /// <param name="logger">日志记录器</param>
    public ATTOController(
        TestToolConfig configuration,
        AutomationHelper automationHelper,
        ILogger<ATTOController> logger)
        : base(configuration)
    {
        _automationHelper = automationHelper ?? throw new ArgumentNullException(nameof(automationHelper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 工具名称
    /// </summary>
    public override string ToolName => "ATTO";

    /// <summary>
    /// 工具类型
    /// </summary>
    public override TestToolType ToolType => TestToolType.CLI;

    /// <summary>
    /// 执行具体的测试逻辑
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>测试结果</returns>
    protected override async Task<TestResult> ExecuteTestInternalAsync(
        TestConfiguration config,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        var result = new TestResult
        {
            ToolName = ToolName,
            StartTime = DateTime.Now
        };

        try
        {
            // 生成测试配置文件
            progress?.Report(new ProgressEventArgs { Progress = 10, Status = "生成ATTO测试配置..." });
            var benchmarkFile = await CreateBenchmarkFileAsync(config, cancellationToken);

            // 执行ATTO测试
            progress?.Report(new ProgressEventArgs { Progress = 30, Status = "执行ATTO磁盘测试..." });
            var success = await ExecuteATTOTestAsync(benchmarkFile, cancellationToken, progress);

            if (success)
            {
                // 解析测试结果
                progress?.Report(new ProgressEventArgs { Progress = 80, Status = "解析测试结果..." });
                await ParseTestResultsAsync(benchmarkFile, result);
                result.Success = true;
            }
            else
            {
                result.Success = false;
                result.ErrorMessage = "ATTO测试执行失败";
            }

            result.EndTime = DateTime.Now;
            _logger.LogInformation("ATTO测试完成，耗时: {Duration}", result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.EndTime = DateTime.Now;
            _logger.LogError(ex, "ATTO测试执行失败: {Message}", ex.Message);
        }

        return result;
    }

    /// <summary>
    /// 创建ATTO基准测试配置文件
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配置文件路径</returns>
    private async Task<string> CreateBenchmarkFileAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            var workingDirectory = Path.GetDirectoryName(_configuration.ExecutablePath) ?? Environment.CurrentDirectory;
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var benchmarkFileName = $"ATTO_Test_{timestamp}_{config.TargetDrive.Replace(":\\", "")}.bmk";
            var benchmarkFilePath = Path.Combine(workingDirectory, benchmarkFileName);

            // 获取配置参数，如果测试配置中没有指定则使用默认值
            var ioRuntime = GetConfigValue<int>(config, "IORuntime", 30);
            var queueDepth = GetConfigValue<int>(config, "QueueDepth", 4);
            var fileSize = GetConfigValue<int>(config, "FileSize", 268435456);
            var verifyData = GetConfigValue<bool>(config, "VerifyData", false);
            var directIO = GetConfigValue<bool>(config, "DirectIO", true);
            var bypassWriteCache = GetConfigValue<bool>(config, "BypassWriteCache", false);
            var continuousIO = GetConfigValue<bool>(config, "ContinuousIO", false);
            var pattern = GetConfigValue<int>(config, "Pattern", 0);
            var ioSize1 = GetConfigValue<int>(config, "IOSize1", 512);
            var ioSize2 = GetConfigValue<int>(config, "IOSize2", 65536);

            _logger.LogDebug("ATTO配置参数: IORuntime={IORuntime}, QueueDepth={QueueDepth}, FileSize={FileSize}",
                ioRuntime, queueDepth, fileSize);

            // 创建XML配置文件
            var benchmarkXml = new XDocument(
                new XDeclaration("1.0", "utf-8", "yes"),
                new XElement("benchmark",
                    new XElement("drive", config.TargetDrive),
                    new XElement("description", $"ATTO Test - {DateTime.Now:yyyy-MM-dd HH:mm:ss}"),
                    new XElement("verifydata", verifyData.ToString().ToLower()),
                    new XElement("directio", directIO.ToString().ToLower()),
                    new XElement("bypasswritecache", bypassWriteCache.ToString().ToLower()),
                    new XElement("continuousio", continuousIO.ToString().ToLower()),
                    new XElement("ioruntime", ioRuntime),
                    new XElement("queuedepth", queueDepth),
                    new XElement("pattern", pattern),
                    new XElement("iosize1", ioSize1),
                    new XElement("iosize2", ioSize2),
                    new XElement("filesize", fileSize),
                    new XElement("rates") // 空的rates节点，测试后会填充结果
                )
            );

            // 使用 SaveOptions.None 确保包含 XML 声明
            using (var writer = new StringWriter())
            {
                benchmarkXml.Save(writer, SaveOptions.None);
                await File.WriteAllTextAsync(benchmarkFilePath, writer.ToString(), cancellationToken);
            }
            _logger.LogInformation("创建ATTO配置文件: {FilePath}", benchmarkFilePath);

            return benchmarkFilePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建ATTO配置文件失败");
            throw;
        }
    }

    /// <summary>
    /// 执行ATTO测试
    /// </summary>
    /// <param name="benchmarkFile">配置文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>执行结果</returns>
    private async Task<bool> ExecuteATTOTestAsync(string benchmarkFile, CancellationToken cancellationToken, IProgress<ProgressEventArgs>? progress = null)
    {
        try
        {
            var arguments = $"-f \"{benchmarkFile}\" -x";
            _logger.LogInformation("执行ATTO命令: {ExecutablePath} {Arguments}", _configuration.ExecutablePath, arguments);

            var processStartInfo = new ProcessStartInfo
            {
                FileName = _configuration.ExecutablePath,
                Arguments = arguments,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = Path.GetDirectoryName(_configuration.ExecutablePath)
            };

            using var process = new Process { StartInfo = processStartInfo };
            var outputBuilder = new StringBuilder();
            var errorBuilder = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    outputBuilder.AppendLine(e.Data);
                    _logger.LogDebug("ATTO输出: {Output}", e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errorBuilder.AppendLine(e.Data);
                    _logger.LogWarning("ATTO错误: {Error}", e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            // 监控进程执行
            var timeout = _configuration.Timeouts?.TestTimeout ?? TimeSpan.FromMinutes(30);
            var progressReporter = new Progress<int>(percent =>
            {
                progress?.Report(new ProgressEventArgs 
                { 
                    Progress = 30 + (percent * 50 / 100), 
                    Status = $"ATTO测试进行中... {percent}%" 
                });
            });

            var completed = await WaitForProcessWithTimeoutAsync(process, timeout, cancellationToken, progressReporter);

            if (!completed)
            {
                _logger.LogError("ATTO测试超时");
                try
                {
                    process.Kill();
                }
                catch (Exception killEx)
                {
                    _logger.LogError(killEx, "终止ATTO进程时出错");
                }
                return false;
            }

            if (process.ExitCode != 0)
            {
                _logger.LogError("ATTO测试失败，退出代码: {ExitCode}, 错误信息: {Error}", 
                    process.ExitCode, errorBuilder.ToString());
                return false;
            }

            _logger.LogInformation("ATTO测试成功完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行ATTO测试时出错");
            return false;
        }
    }

    /// <summary>
    /// 等待进程完成，支持超时和取消
    /// </summary>
    private async Task<bool> WaitForProcessWithTimeoutAsync(Process process, TimeSpan timeout, CancellationToken cancellationToken, IProgress<int>? progress = null)
    {
        var startTime = DateTime.Now;
        var totalMilliseconds = timeout.TotalMilliseconds;

        while (!process.HasExited && !cancellationToken.IsCancellationRequested)
        {
            var elapsed = DateTime.Now - startTime;
            if (elapsed >= timeout)
            {
                return false;
            }

            // 报告进度
            var progressPercent = (int)((elapsed.TotalMilliseconds / totalMilliseconds) * 100);
            progress?.Report(Math.Min(progressPercent, 99));

            await Task.Delay(1000, cancellationToken);
        }

        return process.HasExited && !cancellationToken.IsCancellationRequested;
    }

    /// <summary>
    /// 解析测试结果
    /// </summary>
    /// <param name="benchmarkFile">配置文件路径</param>
    /// <param name="result">测试结果对象</param>
    private async Task ParseTestResultsAsync(string benchmarkFile, TestResult result)
    {
        try
        {
            // 检查日志文件
            var logFile = Path.ChangeExtension(benchmarkFile, ".log");
            if (File.Exists(logFile))
            {
                var logContent = await File.ReadAllTextAsync(logFile);
                _logger.LogInformation("ATTO日志内容: {LogContent}", logContent);
            }

            // 解析更新后的配置文件
            if (!File.Exists(benchmarkFile))
            {
                throw new FileNotFoundException($"配置文件不存在: {benchmarkFile}");
            }

            var xmlContent = await File.ReadAllTextAsync(benchmarkFile);
            var doc = XDocument.Parse(xmlContent);
            var ratesElement = doc.Root?.Element("rates");

            if (ratesElement == null || !ratesElement.HasElements)
            {
                throw new InvalidOperationException("未找到测试结果数据");
            }

            // 解析性能数据
            var performanceData = new Dictionary<string, object>();
            var rates = new List<Dictionary<string, object>>();

            foreach (var rateElement in ratesElement.Elements("rate"))
            {
                var rateData = new Dictionary<string, object>
                {
                    ["IOSize"] = rateElement.Element("iosize")?.Value ?? "0",
                    ["WriteBPS"] = long.Parse(rateElement.Element("writebps")?.Value ?? "0"),
                    ["WriteIOPS"] = long.Parse(rateElement.Element("writeiops")?.Value ?? "0"),
                    ["ReadBPS"] = long.Parse(rateElement.Element("readbps")?.Value ?? "0"),
                    ["ReadIOPS"] = long.Parse(rateElement.Element("readiops")?.Value ?? "0")
                };
                rates.Add(rateData);
            }

            performanceData["Rates"] = rates;
            performanceData["Drive"] = doc.Root?.Element("drive")?.Value ?? "";
            performanceData["TestDuration"] = doc.Root?.Element("ioruntime")?.Value ?? "";
            performanceData["QueueDepth"] = doc.Root?.Element("queuedepth")?.Value ?? "";

            result.Data = performanceData;
            // 将原始输出存储在Data中
            performanceData["RawOutput"] = xmlContent;

            _logger.LogInformation("成功解析ATTO测试结果，包含 {Count} 个性能数据点", rates.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析ATTO测试结果失败");
            throw;
        }
    }

    /// <summary>
    /// 解析测试大小字符串为字节数
    /// </summary>
    /// <param name="testSize">测试大小字符串（如"1GB", "500MB"）</param>
    /// <returns>字节数</returns>
    private long ParseTestSize(string testSize)
    {
        if (string.IsNullOrWhiteSpace(testSize))
            return 268435456; // 默认256MB

        var size = testSize.ToUpperInvariant().Trim();
        var multiplier = 1L;

        if (size.EndsWith("GB"))
        {
            multiplier = 1024L * 1024L * 1024L;
            size = size.Substring(0, size.Length - 2);
        }
        else if (size.EndsWith("MB"))
        {
            multiplier = 1024L * 1024L;
            size = size.Substring(0, size.Length - 2);
        }
        else if (size.EndsWith("KB"))
        {
            multiplier = 1024L;
            size = size.Substring(0, size.Length - 2);
        }

        if (double.TryParse(size, out var value))
        {
            return (long)(value * multiplier);
        }

        return 268435456; // 默认256MB
    }
}
